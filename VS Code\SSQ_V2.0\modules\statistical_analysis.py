#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计分析模块
实现统计分析功能，包括概率排序、特性分析、循环运行和结果保存
"""

import pandas as pd
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from .core_algorithms import CoreAlgorithms
from .utils import Utils


class StatisticalAnalysis:
    """统计分析类"""
    
    def __init__(self, ssq_history: pd.DataFrame, parameters: Dict[str, Any]):
        """
        初始化统计分析
        
        Args:
            ssq_history: SSQ历史数据
            parameters: 参数配置
        """
        self.ssq_history = ssq_history
        self.parameters = parameters
        self.core_algorithms = CoreAlgorithms()
        self.utils = Utils()
        self.results = []
    
    def run_analysis(self, start_period: str, target_period: str) -> bool:
        """
        运行统计分析
        
        Args:
            start_period: 起始期号
            target_period: 目标期号（0表示到最新期）
            
        Returns:
            bool: 是否成功
        """
        try:
            print(f"开始统计分析：从期号 {start_period} 开始")
            
            # 获取分析期号列表
            analysis_periods = self._get_analysis_periods(start_period, target_period)
            
            if not analysis_periods:
                print("没有找到需要分析的期号")
                return False
            
            print(f"需要分析 {len(analysis_periods)} 期数据")
            
            # 循环分析每一期
            for i, period in enumerate(analysis_periods):
                if (i + 1) % 10 == 0:
                    print(f"已完成 {i + 1}/{len(analysis_periods)} 期分析")
                
                self._analyze_single_period(period)
            
            # 保存结果
            success = self._save_results(start_period, target_period)
            
            if success:
                print(f"统计分析完成！共分析 {len(analysis_periods)} 期数据")
                return True
            else:
                print("保存结果失败")
                return False
                
        except Exception as e:
            print(f"统计分析过程中发生错误：{str(e)}")
            return False
    
    def _get_analysis_periods(self, start_period: str, target_period: str) -> List[str]:
        """
        获取需要分析的期号列表
        
        Args:
            start_period: 起始期号
            target_period: 目标期号
            
        Returns:
            List[str]: 期号列表
        """
        start_int = int(start_period)
        
        # 获取所有期号
        all_periods = sorted(self.ssq_history['期号'].tolist())
        
        # 找到起始期号的位置
        start_index = None
        for i, period in enumerate(all_periods):
            if period == start_int:
                start_index = i
                break
        
        if start_index is None:
            return []
        
        # 确定结束期号
        if target_period == '0':
            # 到最新期
            end_index = len(all_periods) - 1
        else:
            target_int = int(target_period)
            end_index = None
            for i, period in enumerate(all_periods):
                if period == target_int:
                    end_index = i - 1  # 目标期号的前一期
                    break
            
            if end_index is None or end_index < start_index:
                return []
        
        # 返回期号列表
        return [str(period) for period in all_periods[start_index:end_index + 1]]
    
    def _analyze_single_period(self, period: str):
        """
        分析单个期号
        
        Args:
            period: 期号
        """
        period_int = int(period)
        
        # 获取当前期的红蓝球号码
        current_row = self.ssq_history[self.ssq_history['期号'] == period_int]
        if current_row.empty:
            return
        
        current_row = current_row.iloc[0]
        red_balls = [current_row['红球1'], current_row['红球2'], current_row['红球3'],
                    current_row['红球4'], current_row['红球5'], current_row['红球6']]
        blue_ball = current_row['蓝球']
        
        # 构建历史概率算法数据库
        hist_db = self._build_history_database(period)
        
        # 构建马尔科夫链算法数据库
        markov_db = self._build_markov_database(period)
        
        # 计算历史出现概率
        red_hist_prob, blue_hist_prob = self.core_algorithms.calculate_historical_probability(hist_db)
        
        # 排序历史概率
        red_hist_sorted = self.core_algorithms.sort_by_probability(red_hist_prob)
        blue_hist_sorted = self.core_algorithms.sort_by_probability(blue_hist_prob)
        
        # 计算跟随性概率
        red_follow_prob, blue_follow_prob = self.core_algorithms.calculate_following_probability(markov_db)
        
        # 获取最新一期数据（用于马尔科夫链）
        latest_data = self._get_latest_data_for_markov(period)
        if latest_data is None:
            return
        
        latest_red, latest_blue = latest_data
        
        # 马尔科夫链算法
        red_migration, blue_migration = self.core_algorithms.markov_chain_algorithm(
            latest_red, latest_blue, red_hist_prob, blue_hist_prob,
            red_follow_prob, blue_follow_prob
        )
        
        # 排序迁移概率
        red_migration_sorted = self.core_algorithms.sort_by_probability(red_migration)
        blue_migration_sorted = self.core_algorithms.sort_by_probability(blue_migration)
        
        # 获取当前期号码在排序表中的位置
        red_hist_rankings = self.core_algorithms.get_ranking_in_sorted_table(red_balls, red_hist_sorted)
        blue_hist_ranking = self.core_algorithms.get_ranking_in_sorted_table([blue_ball], blue_hist_sorted)[0]
        
        red_migration_rankings = self.core_algorithms.get_ranking_in_sorted_table(red_balls, red_migration_sorted)
        blue_migration_ranking = self.core_algorithms.get_ranking_in_sorted_table([blue_ball], blue_migration_sorted)[0]
        
        # 计算遗漏值
        # 使用当前"统计历史出现概率"算法数据库来计算遗漏值
        missing_values = self._calculate_missing_values_correct(red_balls, blue_ball, hist_db)
        
        # 分析特性
        characteristics = self.core_algorithms.analyze_ball_characteristics(red_balls, blue_ball, missing_values)
        
        # 保存结果
        result = {
            '期号': period,
            '红球': red_balls,
            '蓝球': blue_ball,
            '历史概率排序': red_hist_rankings + [blue_hist_ranking],
            '迁移概率排序': red_migration_rankings + [blue_migration_ranking],
            '遗漏值': missing_values,
            '特性': characteristics
        }
        
        self.results.append(result)
    
    def _build_history_database(self, period: str) -> pd.DataFrame:
        """
        构建历史概率算法数据库
        
        Args:
            period: 当前期号
            
        Returns:
            pd.DataFrame: 历史数据库
        """
        period_int = int(period)
        scope = self.parameters['DatabaseScope_history']
        
        # 获取当前期之前的指定期数数据
        filtered_data = self.ssq_history[self.ssq_history['期号'] < period_int]
        
        if len(filtered_data) <= scope:
            return filtered_data
        else:
            return filtered_data.tail(scope)
    
    def _build_markov_database(self, period: str) -> pd.DataFrame:
        """
        构建马尔科夫链算法数据库
        
        Args:
            period: 当前期号
            
        Returns:
            pd.DataFrame: 马尔科夫数据库
        """
        period_int = int(period)
        scope = self.parameters['DatabaseScope_follow']
        
        # 获取当前期之前的指定期数数据
        filtered_data = self.ssq_history[self.ssq_history['期号'] < period_int]
        
        if len(filtered_data) <= scope:
            return filtered_data
        else:
            return filtered_data.tail(scope)
    
    def _build_missing_database(self, period: str) -> pd.DataFrame:
        """
        构建遗漏期数据库（用于计算遗漏值）
        
        Args:
            period: 当前期号
            
        Returns:
            pd.DataFrame: 遗漏数据库
        """
        period_int = int(period)
        
        # 获取当前期之前的所有数据
        return self.ssq_history[self.ssq_history['期号'] < period_int]
    
    def _get_latest_data_for_markov(self, period: str) -> Optional[tuple]:
        """
        获取马尔科夫链算法所需的最新一期数据
        
        Args:
            period: 当前期号
            
        Returns:
            Optional[tuple]: (最新红球列表, 最新蓝球) 或 None
        """
        markov_db = self._build_markov_database(period)
        
        if markov_db.empty:
            return None
        
        # 获取最新一期数据
        latest_row = markov_db.iloc[-1]
        latest_red = [latest_row['红球1'], latest_row['红球2'], latest_row['红球3'],
                     latest_row['红球4'], latest_row['红球5'], latest_row['红球6']]
        latest_blue = latest_row['蓝球']
        
        return latest_red, latest_blue

    def _calculate_missing_values_correct(self, red_balls: List[int], blue_ball: int,
                                        hist_db: pd.DataFrame) -> List[int]:
        """
        正确计算遗漏值
        将起始期号对应的7个号码，加在当前"统计历史出现概率"算法数据库中最新1期号码之后，
        以统计该组号码的遗漏值

        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
            hist_db: 历史数据库

        Returns:
            List[int]: 遗漏值列表（6个红球+1个蓝球）
        """
        missing_values = []

        # 计算红球遗漏值
        for ball in red_balls:
            missing = self._calculate_single_ball_missing(ball, False, hist_db)
            missing_values.append(missing)

        # 计算蓝球遗漏值
        blue_missing = self._calculate_single_ball_missing(blue_ball, True, hist_db)
        missing_values.append(blue_missing)

        return missing_values

    def _calculate_single_ball_missing(self, ball: int, is_blue: bool, hist_db: pd.DataFrame) -> int:
        """
        计算单个号码的遗漏值

        Args:
            ball: 号码
            is_blue: 是否为蓝球
            hist_db: 历史数据库

        Returns:
            int: 遗漏值
        """
        # 从最新到最旧搜索该号码最后一次出现的位置
        for i in range(len(hist_db) - 1, -1, -1):
            row = hist_db.iloc[i]

            if is_blue:
                if row['蓝球'] == ball:
                    # 遗漏值 = 从该位置到数据库末尾的期数
                    return len(hist_db) - 1 - i
            else:
                red_balls_in_row = [row['红球1'], row['红球2'], row['红球3'],
                                  row['红球4'], row['红球5'], row['红球6']]
                if ball in red_balls_in_row:
                    # 遗漏值 = 从该位置到数据库末尾的期数
                    return len(hist_db) - 1 - i

        # 如果在历史数据库中没找到，返回数据库长度
        return len(hist_db)

    def _save_results(self, start_period: str, target_period: str) -> bool:
        """
        保存分析结果到Excel文件

        Args:
            start_period: 起始期号
            target_period: 目标期号

        Returns:
            bool: 是否成功
        """
        try:
            # 生成文件名
            if target_period == '0':
                latest_period = str(self.ssq_history['期号'].max())
                filename = self.utils.generate_filename(
                    "统计分析SSQ", latest_period,
                    self.parameters['DatabaseScope_history'],
                    self.parameters['DatabaseScope_follow']
                )
            else:
                filename = self.utils.generate_filename(
                    "统计分析SSQ", target_period,
                    self.parameters['DatabaseScope_history'],
                    self.parameters['DatabaseScope_follow']
                )

            # 确保Output文件夹存在
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Output")
            os.makedirs(output_dir, exist_ok=True)

            file_path = os.path.join(output_dir, filename)

            # 创建Excel写入器
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 保存历史出现概率排序
                self._save_history_probability_ranking(writer)

                # 保存号码迁移概率排序
                self._save_migration_probability_ranking(writer)

                # 保存号码遗漏值特性
                self._save_missing_values(writer)

                # 保存基础特性
                self._save_basic_characteristics(writer)

            print(f"结果已保存到：{file_path}")
            return True

        except Exception as e:
            print(f"保存结果失败：{str(e)}")
            return False

    def _save_history_probability_ranking(self, writer):
        """保存历史出现概率排序数据"""
        data = []
        for result in self.results:
            row = [result['期号']] + result['红球'] + [result['蓝球']] + result['历史概率排序']
            data.append(row)

        columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球',
                  '红球1排序', '红球2排序', '红球3排序', '红球4排序', '红球5排序', '红球6排序', '蓝球排序']

        df = pd.DataFrame(data, columns=columns)
        df.to_excel(writer, sheet_name='历史出现概率排序', index=False)

    def _save_migration_probability_ranking(self, writer):
        """保存号码迁移概率排序数据"""
        data = []
        for result in self.results:
            row = [result['期号']] + result['红球'] + [result['蓝球']] + result['迁移概率排序']
            data.append(row)

        columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球',
                  '红球1排序', '红球2排序', '红球3排序', '红球4排序', '红球5排序', '红球6排序', '蓝球排序']

        df = pd.DataFrame(data, columns=columns)
        df.to_excel(writer, sheet_name='号码迁移概率排序', index=False)

    def _save_missing_values(self, writer):
        """保存号码遗漏值特性数据"""
        data = []
        for result in self.results:
            row = [result['期号']] + result['红球'] + [result['蓝球']] + result['遗漏值']
            data.append(row)

        columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球',
                  '红球1遗漏', '红球2遗漏', '红球3遗漏', '红球4遗漏', '红球5遗漏', '红球6遗漏', '蓝球遗漏']

        df = pd.DataFrame(data, columns=columns)
        df.to_excel(writer, sheet_name='号码遗漏值特性', index=False)

    def _save_basic_characteristics(self, writer):
        """保存基础特性数据"""
        data = []
        for result in self.results:
            characteristics = result['特性']
            row = [
                result['期号'],
                *result['红球'],
                result['蓝球'],
                characteristics['红球大球个数'],
                characteristics['红球奇球个数'],
                characteristics['红球质球个数'],
                characteristics['红球和值'],
                characteristics['红球AC值']
            ]
            data.append(row)

        columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球',
                  '红球大球个数', '红球奇球个数', '红球质球个数', '红球和值', '红球AC值']

        df = pd.DataFrame(data, columns=columns)
        df.to_excel(writer, sheet_name='基础特性', index=False)
