#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具模块
提供各种辅助功能和计算方法
"""

import re
import math
import itertools
from typing import List, Tuple, Set
from datetime import datetime


class Utils:
    """工具类"""
    
    @staticmethod
    def validate_period_format(period: str) -> bool:
        """
        验证期号格式
        
        Args:
            period: 期号字符串
            
        Returns:
            bool: 格式是否正确
        """
        # 期号格式：4位数（如7001）或5位数（如14100）
        pattern = r'^\d{4,5}$'
        return bool(re.match(pattern, period))
    
    @staticmethod
    def is_big_ball(number: int, is_blue: bool = False) -> bool:
        """
        判断是否为大球号码
        
        Args:
            number: 号码
            is_blue: 是否为蓝球
            
        Returns:
            bool: 是否为大球号码
        """
        if is_blue:
            return number > 8  # 蓝球大于8为大球
        else:
            return number > 16  # 红球大于16为大球
    
    @staticmethod
    def is_odd_ball(number: int) -> bool:
        """
        判断是否为奇球号码
        
        Args:
            number: 号码
            
        Returns:
            bool: 是否为奇球号码
        """
        return number % 2 == 1
    
    @staticmethod
    def is_prime_ball(number: int) -> bool:
        """
        判断是否为质球号码（包括1）
        
        Args:
            number: 号码
            
        Returns:
            bool: 是否为质球号码
        """
        if number == 1:
            return True
        if number < 2:
            return False
        if number == 2:
            return True
        if number % 2 == 0:
            return False
        
        for i in range(3, int(math.sqrt(number)) + 1, 2):
            if number % i == 0:
                return False
        return True
    
    @staticmethod
    def calculate_sum(red_balls: List[int]) -> int:
        """
        计算红球号码和值
        
        Args:
            red_balls: 红球号码列表
            
        Returns:
            int: 和值
        """
        return sum(red_balls)
    
    @staticmethod
    def calculate_ac_value(red_balls: List[int]) -> int:
        """
        计算AC值（号码数字复杂度）
        
        Args:
            red_balls: 红球号码列表（已排序）
            
        Returns:
            int: AC值
        """
        if len(red_balls) < 2:
            return 0
        
        # 计算任意两个号码之间不相同的正差值
        differences = set()
        for i in range(len(red_balls)):
            for j in range(i + 1, len(red_balls)):
                diff = abs(red_balls[j] - red_balls[i])
                differences.add(diff)
        
        # AC值 = 不相同的正差值总个数 - (正选号码数量-1)
        ac_value = len(differences) - (len(red_balls) - 1)
        return ac_value
    
    @staticmethod
    def calculate_missing_values(current_period: str, red_balls: List[int], blue_ball: int, 
                               history_data, missing_db) -> List[int]:
        """
        计算号码遗漏值
        
        Args:
            current_period: 当前期号
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
            history_data: 历史数据
            missing_db: 遗漏期数据库
            
        Returns:
            List[int]: 遗漏值列表（6个红球+1个蓝球）
        """
        missing_values = []
        
        # 计算红球遗漏值
        for ball in red_balls:
            missing = Utils._calculate_single_missing(current_period, ball, False, missing_db)
            missing_values.append(missing)
        
        # 计算蓝球遗漏值
        blue_missing = Utils._calculate_single_missing(current_period, blue_ball, True, missing_db)
        missing_values.append(blue_missing)
        
        return missing_values
    
    @staticmethod
    def _calculate_single_missing(current_period: str, ball: int, is_blue: bool, missing_db) -> int:
        """
        计算单个号码的遗漏值
        
        Args:
            current_period: 当前期号
            ball: 号码
            is_blue: 是否为蓝球
            missing_db: 遗漏期数据库
            
        Returns:
            int: 遗漏值
        """
        # 在遗漏期数据库中查找该号码最后一次出现的期号
        last_period = None
        current_period_int = int(current_period)
        
        # 从最新到最旧搜索
        for _, row in missing_db.iterrows():
            period = int(row['期号'])
            if period >= current_period_int:
                continue
                
            if is_blue:
                if row['蓝球'] == ball:
                    last_period = period
                    break
            else:
                red_balls = [row['红球1'], row['红球2'], row['红球3'], 
                           row['红球4'], row['红球5'], row['红球6']]
                if ball in red_balls:
                    last_period = period
                    break
        
        if last_period is None:
            return len(missing_db)  # 如果没找到，返回数据库长度
        
        # 计算期数差
        return Utils._calculate_period_difference(last_period, current_period_int)
    
    @staticmethod
    def _calculate_period_difference(last_period: int, current_period: int) -> int:
        """
        计算两个期号之间的期数差
        
        Args:
            last_period: 上次出现期号
            current_period: 当前期号
            
        Returns:
            int: 期数差
        """
        # 简化计算：直接按期号顺序计算
        # 实际应用中需要考虑跨年的情况
        return current_period - last_period - 1
    
    @staticmethod
    def get_next_period(period: str, history_data) -> str:
        """
        获取下一期期号
        
        Args:
            period: 当前期号
            history_data: 历史数据
            
        Returns:
            str: 下一期期号，如果没有则返回None
        """
        period_int = int(period)
        periods = sorted(history_data['期号'].tolist())
        
        for i, p in enumerate(periods):
            if p == period_int and i + 1 < len(periods):
                return str(periods[i + 1])
        
        return None
    
    @staticmethod
    def format_balls_display(red_balls: List[int], blue_ball: int) -> str:
        """
        格式化红蓝球号码显示
        
        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
            
        Returns:
            str: 格式化的显示字符串
        """
        red_str = ' '.join([f"{ball:02d}" for ball in sorted(red_balls)])
        blue_str = f"{blue_ball:02d}"
        return f"{red_str} + {blue_str}"
    
    @staticmethod
    def generate_filename(prefix: str, target_period: str, scope_history: int, 
                         scope_follow: int, missing_range: int = None) -> str:
        """
        生成文件名
        
        Args:
            prefix: 文件名前缀
            target_period: 目标期号
            scope_history: 历史数据范围
            scope_follow: 跟随数据范围
            missing_range: 遗漏期范围（可选）
            
        Returns:
            str: 生成的文件名
        """
        date_str = datetime.now().strftime("%Y%m%d")
        
        if missing_range is not None:
            return f"{prefix}_{date_str}_{target_period}_{scope_history}_{scope_follow}_{missing_range}.xlsx"
        else:
            return f"{prefix}_{date_str}_{target_period}_{scope_history}_{scope_follow}.xlsx"
    
    @staticmethod
    def combinations_count(n: int, r: int) -> int:
        """
        计算组合数C(n,r)
        
        Args:
            n: 总数
            r: 选择数
            
        Returns:
            int: 组合数
        """
        if r > n or r < 0:
            return 0
        return math.factorial(n) // (math.factorial(r) * math.factorial(n - r))
    
    @staticmethod
    def get_all_combinations(red_balls: List[int], blue_balls: List[int]) -> List[Tuple[List[int], int]]:
        """
        获取所有红蓝球组合
        
        Args:
            red_balls: 红球号码列表
            blue_balls: 蓝球号码列表
            
        Returns:
            List[Tuple[List[int], int]]: 所有组合列表
        """
        combinations = []
        
        # 生成6个红球的所有组合
        for red_combo in itertools.combinations(red_balls, 6):
            for blue in blue_balls:
                combinations.append((list(red_combo), blue))
        
        return combinations
