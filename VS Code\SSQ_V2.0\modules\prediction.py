#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测选号模块
实现预测选号功能，包括初选号码、筛选号码和结果保存
"""

import pandas as pd
import os
import tkinter as tk
from tkinter import filedialog
from datetime import datetime
from typing import Dict, List, Any, Tuple, Set
from itertools import combinations
from .core_algorithms import CoreAlgorithms
from .utils import Utils
from .data_reader import DataReader


class Prediction:
    """预测选号类"""
    
    def __init__(self, ssq_history: pd.DataFrame, parameters: Dict[str, Any]):
        """
        初始化预测选号
        
        Args:
            ssq_history: SSQ历史数据
            parameters: 参数配置
        """
        self.ssq_history = ssq_history
        self.parameters = parameters
        self.core_algorithms = CoreAlgorithms()
        self.utils = Utils()
        self.data_reader = DataReader()
    
    def run_prediction(self, target_period: str, missing_range: int) -> bool:
        """
        运行预测选号
        
        Args:
            target_period: 目标期号（0表示预测下一期）
            missing_range: 遗漏期数据库范围
            
        Returns:
            bool: 是否成功
        """
        try:
            print(f"开始预测选号：目标期号 {target_period}，遗漏期范围 {missing_range}")
            
            # 选择统计分析文件
            analysis_file = self._select_analysis_file()
            if not analysis_file:
                print("未选择统计分析文件")
                return False
            
            # 读取统计分析数据
            analysis_data = self.data_reader.read_analysis_file(analysis_file)
            
            # 构建数据库
            databases = self._build_databases(target_period, missing_range)
            
            # 统计排序序号次数并生成表格
            ranking_stats = self._calculate_ranking_statistics(analysis_data)
            
            # 初选号码
            candidate_groups = self._initial_selection(databases, ranking_stats)
            
            # 筛选号码
            filtered_combinations = self._filter_combinations(candidate_groups)

            # 打印各组信息
            self._print_group_info(candidate_groups, filtered_combinations)
            
            # 保存结果
            success = self._save_prediction_results(target_period, missing_range, 
                                                  filtered_combinations, databases)
            
            if success:
                print("预测选号完成！结果已保存到Output文件夹")
                return True
            else:
                print("保存结果失败")
                return False
                
        except Exception as e:
            print(f"预测选号过程中发生错误：{str(e)}")
            return False
    
    def _select_analysis_file(self) -> str:
        """
        选择统计分析文件
        
        Returns:
            str: 选择的文件路径，如果取消则返回空字符串
        """
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()
            
            # 设置文件对话框的初始目录
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Output")
            
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择统计分析SSQ文件",
                initialdir=output_dir,
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            root.destroy()
            return file_path
            
        except Exception as e:
            print(f"文件选择失败：{str(e)}")
            return ""
    
    def _build_databases(self, target_period: str, missing_range: int) -> Dict[str, pd.DataFrame]:
        """
        构建各种数据库
        
        Args:
            target_period: 目标期号
            missing_range: 遗漏期范围
            
        Returns:
            Dict[str, pd.DataFrame]: 各种数据库
        """
        databases = {}
        
        if target_period == '0':
            # 预测下一期
            latest_period = self.ssq_history['期号'].max()
            
            # 遗漏期数据库：最新一期及之前的missing_range-1期
            missing_db = self.ssq_history.tail(missing_range)
            
            # 历史概率数据库：最新一期及之前的DatabaseScope_history-1期
            hist_scope = self.parameters['DatabaseScope_history']
            hist_db = self.ssq_history.tail(hist_scope)
            
            # 马尔科夫数据库：最新一期及之前的DatabaseScope_follow-1期
            follow_scope = self.parameters['DatabaseScope_follow']
            markov_db = self.ssq_history.tail(follow_scope)
            
        else:
            # 预测指定期号
            target_int = int(target_period)
            
            # 遗漏期数据库：目标期号之前的missing_range期
            missing_db = self.ssq_history[self.ssq_history['期号'] < target_int].tail(missing_range)
            
            # 历史概率数据库：目标期号之前的DatabaseScope_history期
            hist_scope = self.parameters['DatabaseScope_history']
            hist_db = self.ssq_history[self.ssq_history['期号'] < target_int].tail(hist_scope)
            
            # 马尔科夫数据库：目标期号之前的DatabaseScope_follow期
            follow_scope = self.parameters['DatabaseScope_follow']
            markov_db = self.ssq_history[self.ssq_history['期号'] < target_int].tail(follow_scope)
        
        databases['missing'] = missing_db
        databases['history'] = hist_db
        databases['markov'] = markov_db
        
        return databases
    
    def _calculate_ranking_statistics(self, analysis_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        统计排序序号次数并生成表格
        
        Args:
            analysis_data: 统计分析数据
            
        Returns:
            Dict[str, Any]: 排序统计结果
        """
        # 获取历史出现概率排序数据
        hist_data = analysis_data['历史出现概率排序']
        
        # 统计红球排序序号出现次数
        red_ranking_counts = {}
        for i in range(1, 34):
            red_ranking_counts[i] = 0
        
        # 统计蓝球排序序号出现次数
        blue_ranking_counts = {}
        for i in range(1, 17):
            blue_ranking_counts[i] = 0
        
        # 遍历数据统计
        red_cols = ['红球1排序', '红球2排序', '红球3排序', '红球4排序', '红球5排序', '红球6排序']
        for _, row in hist_data.iterrows():
            for col in red_cols:
                ranking = int(row[col])
                if 1 <= ranking <= 33:
                    red_ranking_counts[ranking] += 1
            
            blue_ranking = int(row['蓝球排序'])
            if 1 <= blue_ranking <= 16:
                blue_ranking_counts[blue_ranking] += 1
        
        # 生成排序表格并获取前N项
        red_ranking_table = self._create_ranking_table(red_ranking_counts)
        blue_ranking_table = self._create_ranking_table(blue_ranking_counts)
        
        # 获取前N项排序序号
        history_num_red = self.parameters['HistoryNum_Red']
        history_num_blue = self.parameters['HistoryNum_Blue']
        
        HXR = red_ranking_table.head(history_num_red)['排序序号'].tolist()
        HXB = blue_ranking_table.head(history_num_blue)['排序序号'].tolist()
        
        # 对号码迁移概率排序做同样处理
        migration_data = analysis_data['号码迁移概率排序']
        
        # 统计迁移概率排序序号出现次数
        red_migration_counts = {}
        for i in range(1, 34):
            red_migration_counts[i] = 0
        
        blue_migration_counts = {}
        for i in range(1, 17):
            blue_migration_counts[i] = 0
        
        for _, row in migration_data.iterrows():
            for col in red_cols:
                ranking = int(row[col])
                if 1 <= ranking <= 33:
                    red_migration_counts[ranking] += 1
            
            blue_ranking = int(row['蓝球排序'])
            if 1 <= blue_ranking <= 16:
                blue_migration_counts[blue_ranking] += 1
        
        red_migration_table = self._create_ranking_table(red_migration_counts)
        blue_migration_table = self._create_ranking_table(blue_migration_counts)
        
        follow_num_red = self.parameters['FollowNum_Red']
        follow_num_blue = self.parameters['FollowNum_Blue']
        
        FXR = red_migration_table.head(follow_num_red)['排序序号'].tolist()
        FXB = blue_migration_table.head(follow_num_blue)['排序序号'].tolist()
        
        return {
            'HXR': HXR,
            'HXB': HXB,
            'FXR': FXR,
            'FXB': FXB,
            'red_hist_table': red_ranking_table,
            'blue_hist_table': blue_ranking_table,
            'red_migration_table': red_migration_table,
            'blue_migration_table': blue_migration_table
        }
    
    def _create_ranking_table(self, ranking_counts: Dict[int, int]) -> pd.DataFrame:
        """
        创建排序表格
        
        Args:
            ranking_counts: 排序序号出现次数字典
            
        Returns:
            pd.DataFrame: 排序表格
        """
        data = []
        for ranking, count in ranking_counts.items():
            data.append({'排序序号': ranking, '出现次数': count})
        
        df = pd.DataFrame(data)
        # 按出现次数从多到少排序，次数相等时按序号从小到大排序
        df = df.sort_values(['出现次数', '排序序号'], ascending=[False, True]).reset_index(drop=True)
        
        return df

    def _initial_selection(self, databases: Dict[str, pd.DataFrame],
                          ranking_stats: Dict[str, Any]) -> Dict[str, Tuple[Set[int], Set[int]]]:
        """
        初选号码

        Args:
            databases: 各种数据库
            ranking_stats: 排序统计结果

        Returns:
            Dict[str, Tuple[Set[int], Set[int]]]: 各组复式红蓝球号码
        """
        groups = {}

        # 第1组：基于遗漏期数据库的去重复号码
        missing_db = databases['missing']
        red_set1 = set()
        blue_set1 = set()

        for _, row in missing_db.iterrows():
            red_balls = [row['红球1'], row['红球2'], row['红球3'],
                        row['红球4'], row['红球5'], row['红球6']]
            red_set1.update(red_balls)
            blue_set1.add(row['蓝球'])

        groups['group1'] = (red_set1, blue_set1)

        # 第2组：基于历史出现概率的选择
        hist_db = databases['history']
        red_hist_prob, blue_hist_prob = self.core_algorithms.calculate_historical_probability(hist_db)

        # 排序概率
        red_hist_sorted = self.core_algorithms.sort_by_probability(red_hist_prob)
        blue_hist_sorted = self.core_algorithms.sort_by_probability(blue_hist_prob)

        # 根据HXR和HXB选择号码
        HXR = ranking_stats['HXR']
        HXB = ranking_stats['HXB']

        red_set2 = set()
        for rank in HXR:
            if rank <= len(red_hist_sorted):
                ball = red_hist_sorted.iloc[rank - 1]['号码']
                red_set2.add(ball)

        blue_set2 = set()
        for rank in HXB:
            if rank <= len(blue_hist_sorted):
                ball = blue_hist_sorted.iloc[rank - 1]['号码']
                blue_set2.add(ball)

        groups['group2'] = (red_set2, blue_set2)

        # 第3组：基于马尔科夫链算法的选择
        markov_db = databases['markov']

        if not markov_db.empty:
            # 获取最新一期数据
            latest_row = markov_db.iloc[-1]
            latest_red = [latest_row['红球1'], latest_row['红球2'], latest_row['红球3'],
                         latest_row['红球4'], latest_row['红球5'], latest_row['红球6']]
            latest_blue = latest_row['蓝球']

            # 计算跟随概率
            red_follow_prob, blue_follow_prob = self.core_algorithms.calculate_following_probability(markov_db)

            # 马尔科夫链算法
            red_migration, blue_migration = self.core_algorithms.markov_chain_algorithm(
                latest_red, latest_blue, red_hist_prob, blue_hist_prob,
                red_follow_prob, blue_follow_prob
            )

            # 排序迁移概率
            red_migration_sorted = self.core_algorithms.sort_by_probability(red_migration)
            blue_migration_sorted = self.core_algorithms.sort_by_probability(blue_migration)

            # 根据FXR和FXB选择号码
            FXR = ranking_stats['FXR']
            FXB = ranking_stats['FXB']

            red_set3 = set()
            for rank in FXR:
                if rank <= len(red_migration_sorted):
                    ball = red_migration_sorted.iloc[rank - 1]['号码']
                    red_set3.add(ball)

            blue_set3 = set()
            for rank in FXB:
                if rank <= len(blue_migration_sorted):
                    ball = blue_migration_sorted.iloc[rank - 1]['号码']
                    blue_set3.add(ball)

            groups['group3'] = (red_set3, blue_set3)
        else:
            groups['group3'] = (set(), set())

        return groups

    def _print_group_info(self, candidate_groups: Dict[str, Tuple[Set[int], Set[int]]],
                         filtered_combinations: List[Tuple[List[int], int]]):
        """
        打印各组复式红蓝球号码信息

        Args:
            candidate_groups: 候选号码组
            filtered_combinations: 筛选后的组合
        """
        print("\n" + "=" * 50)
        print("各组复式红蓝球号码统计：")
        print("=" * 50)

        # 打印前3组信息
        for i, group_name in enumerate(['group1', 'group2', 'group3'], 1):
            if group_name in candidate_groups:
                red_set, blue_set = candidate_groups[group_name]
                print(f"第{i}组复式红蓝球号码包括 {len(red_set)} 个红球号码，{len(blue_set)} 个蓝球号码")

        # 计算第4组（交集）
        red_intersection = candidate_groups['group1'][0]
        blue_intersection = candidate_groups['group1'][1]

        for group_name in ['group2', 'group3']:
            if group_name in candidate_groups:
                red_intersection = red_intersection.intersection(candidate_groups[group_name][0])
                blue_intersection = blue_intersection.intersection(candidate_groups[group_name][1])

        # 如果蓝球交集为空，设为0
        if not blue_intersection:
            blue_intersection = {0}

        print(f"第4组复式红蓝球号码包括 {len(red_intersection)} 个红球号码，{len(blue_intersection)} 个蓝球号码")
        print(f"筛选之后，还剩 {len(filtered_combinations)} 组红蓝球号码")
        print("=" * 50)

    def _filter_combinations(self, candidate_groups: Dict[str, Tuple[Set[int], Set[int]]]) -> List[Tuple[List[int], int]]:
        """
        筛选号码组合

        Args:
            candidate_groups: 候选号码组

        Returns:
            List[Tuple[List[int], int]]: 筛选后的组合列表
        """
        # 求交集得到第4组
        red_intersection = candidate_groups['group1'][0]
        blue_intersection = candidate_groups['group1'][1]

        for group_name in ['group2', 'group3']:
            if group_name in candidate_groups:
                red_intersection = red_intersection.intersection(candidate_groups[group_name][0])
                blue_intersection = blue_intersection.intersection(candidate_groups[group_name][1])

        # 如果蓝球交集为空，设为0
        if not blue_intersection:
            blue_intersection = {0}

        # 生成所有可能的组合
        if len(red_intersection) < 6:
            print(f"红球交集数量不足6个：{len(red_intersection)}")
            return []

        all_combinations = []
        red_list = list(red_intersection)
        blue_list = list(blue_intersection)

        # 生成6个红球的组合
        for red_combo in combinations(red_list, 6):
            for blue in blue_list:
                if blue == 0:  # 跳过蓝球为0的情况
                    continue
                all_combinations.append((list(red_combo), blue))

        # 筛选组合
        filtered_combinations = []

        for red_balls, blue_ball in all_combinations:
            if self._check_combination_criteria(red_balls, blue_ball):
                filtered_combinations.append((red_balls, blue_ball))

        return filtered_combinations

    def _check_combination_criteria(self, red_balls: List[int], blue_ball: int) -> bool:
        """
        检查组合是否符合筛选条件

        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码

        Returns:
            bool: 是否符合条件
        """
        # 1. 检查是否与历史数据重复
        if self._is_duplicate_with_history(red_balls, blue_ball):
            return False

        # 2. 检查红球大球号码数量
        big_count = sum(1 for ball in red_balls if self.utils.is_big_ball(ball, False))
        if not (self.parameters['NumRB_min'] <= big_count <= self.parameters['NumRB_max']):
            return False

        # 3. 检查红球奇球号码数量
        odd_count = sum(1 for ball in red_balls if self.utils.is_odd_ball(ball))
        if not (self.parameters['NumRQ_min'] <= odd_count <= self.parameters['NumRQ_max']):
            return False

        # 4. 检查红球质球号码数量
        prime_count = sum(1 for ball in red_balls if self.utils.is_prime_ball(ball))
        if not (self.parameters['NumRZ_min'] <= prime_count <= self.parameters['NumRZ_max']):
            return False

        # 5. 检查红球和值
        sum_value = self.utils.calculate_sum(red_balls)
        if not (self.parameters['SumR_min'] <= sum_value <= self.parameters['SumR_max']):
            return False

        # 6. 检查红球AC值
        ac_value = self.utils.calculate_ac_value(sorted(red_balls))
        if not (self.parameters['NumRAC_min'] <= ac_value <= self.parameters['NumRAC_max']):
            return False

        return True

    def _is_duplicate_with_history(self, red_balls: List[int], blue_ball: int) -> bool:
        """
        检查是否与历史数据重复

        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码

        Returns:
            bool: 是否重复
        """
        red_set = set(red_balls)

        for _, row in self.ssq_history.iterrows():
            hist_red = {row['红球1'], row['红球2'], row['红球3'],
                       row['红球4'], row['红球5'], row['红球6']}
            hist_blue = row['蓝球']

            if red_set == hist_red and blue_ball == hist_blue:
                return True

        return False

    def _save_prediction_results(self, target_period: str, missing_range: int,
                               filtered_combinations: List[Tuple[List[int], int]],
                               databases: Dict[str, pd.DataFrame]) -> bool:
        """
        保存预测结果

        Args:
            target_period: 目标期号
            missing_range: 遗漏期范围
            filtered_combinations: 筛选后的组合
            databases: 数据库

        Returns:
            bool: 是否成功
        """
        try:
            # 生成文件名
            if target_period == '0':
                latest_period = str(self.ssq_history['期号'].max())
                filename = self.utils.generate_filename(
                    "筛选号码SSQ", latest_period,
                    self.parameters['DatabaseScope_history'],
                    self.parameters['DatabaseScope_follow'],
                    missing_range
                )
            else:
                filename = self.utils.generate_filename(
                    "筛选号码SSQ", target_period,
                    self.parameters['DatabaseScope_history'],
                    self.parameters['DatabaseScope_follow'],
                    missing_range
                )

            # 确保Output文件夹存在
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Output")
            os.makedirs(output_dir, exist_ok=True)

            file_path = os.path.join(output_dir, filename)

            # 创建Excel写入器
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 保存详细结果
                self._save_detailed_results(writer, filtered_combinations, databases)

                # 保存概率表
                self._save_probability_tables(writer, databases)

            print(f"预测结果已保存到：{file_path}")
            return True

        except Exception as e:
            print(f"保存预测结果失败：{str(e)}")
            return False

    def _save_detailed_results(self, writer, filtered_combinations: List[Tuple[List[int], int]],
                             databases: Dict[str, pd.DataFrame]):
        """保存详细结果"""
        data = []

        missing_db = databases['missing']
        hist_db = databases['history']
        markov_db = databases['markov']

        # 计算概率表
        red_hist_prob, blue_hist_prob = self.core_algorithms.calculate_historical_probability(hist_db)

        if not markov_db.empty:
            latest_row = markov_db.iloc[-1]
            latest_red = [latest_row['红球1'], latest_row['红球2'], latest_row['红球3'],
                         latest_row['红球4'], latest_row['红球5'], latest_row['红球6']]
            latest_blue = latest_row['蓝球']

            red_follow_prob, blue_follow_prob = self.core_algorithms.calculate_following_probability(markov_db)
            red_migration, blue_migration = self.core_algorithms.markov_chain_algorithm(
                latest_red, latest_blue, red_hist_prob, blue_hist_prob,
                red_follow_prob, blue_follow_prob
            )
        else:
            red_migration = pd.DataFrame({'号码': range(1, 34), '概率': [0] * 33})
            blue_migration = pd.DataFrame({'号码': range(1, 17), '概率': [0] * 16})

        for red_balls, blue_ball in filtered_combinations:
            # 计算遗漏值
            missing_values = self.utils.calculate_missing_values(
                "999999", red_balls, blue_ball, self.ssq_history, missing_db
            )

            # 获取历史概率
            red_hist_probs = []
            for ball in red_balls:
                prob_row = red_hist_prob[red_hist_prob['号码'] == ball]
                if not prob_row.empty:
                    red_hist_probs.append(prob_row.iloc[0]['概率'])
                else:
                    red_hist_probs.append(0)

            blue_hist_prob_val = 0
            blue_prob_row = blue_hist_prob[blue_hist_prob['号码'] == blue_ball]
            if not blue_prob_row.empty:
                blue_hist_prob_val = blue_prob_row.iloc[0]['概率']

            # 获取迁移概率
            red_migration_probs = []
            for ball in red_balls:
                prob_row = red_migration[red_migration['号码'] == ball]
                if not prob_row.empty:
                    red_migration_probs.append(prob_row.iloc[0]['概率'])
                else:
                    red_migration_probs.append(0)

            blue_migration_prob_val = 0
            blue_prob_row = blue_migration[blue_migration['号码'] == blue_ball]
            if not blue_prob_row.empty:
                blue_migration_prob_val = blue_prob_row.iloc[0]['概率']

            # 构建数据行
            row = (sorted(red_balls) + [blue_ball] +
                   missing_values +
                   red_hist_probs + [blue_hist_prob_val] +
                   red_migration_probs + [blue_migration_prob_val])

            data.append(row)

        # 创建列名
        columns = (['红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'] +
                  ['红球1遗漏', '红球2遗漏', '红球3遗漏', '红球4遗漏', '红球5遗漏', '红球6遗漏', '蓝球遗漏'] +
                  ['红球1历史概率', '红球2历史概率', '红球3历史概率', '红球4历史概率', '红球5历史概率', '红球6历史概率', '蓝球历史概率'] +
                  ['红球1迁移概率', '红球2迁移概率', '红球3迁移概率', '红球4迁移概率', '红球5迁移概率', '红球6迁移概率', '蓝球迁移概率'])

        df = pd.DataFrame(data, columns=columns)
        df.to_excel(writer, sheet_name='详细结果', index=False)

    def _save_probability_tables(self, writer, databases: Dict[str, pd.DataFrame]):
        """保存概率表"""
        hist_db = databases['history']
        markov_db = databases['markov']

        # 计算概率
        red_hist_prob, blue_hist_prob = self.core_algorithms.calculate_historical_probability(hist_db)

        # 保存红球历史概率
        red_hist_prob.to_excel(writer, sheet_name='红球历史出现概率', index=False)

        # 保存蓝球历史概率
        blue_hist_prob.to_excel(writer, sheet_name='蓝球历史出现概率', index=False)

        if not markov_db.empty:
            # 计算跟随概率
            red_follow_prob, blue_follow_prob = self.core_algorithms.calculate_following_probability(markov_db)

            # 保存跟随概率矩阵
            red_follow_df = pd.DataFrame(red_follow_prob)
            red_follow_df.to_excel(writer, sheet_name='红球号码历史跟随性概率', index=False)

            blue_follow_df = pd.DataFrame(blue_follow_prob)
            blue_follow_df.to_excel(writer, sheet_name='蓝球号码历史跟随性概率', index=False)

            # 计算迁移概率
            latest_row = markov_db.iloc[-1]
            latest_red = [latest_row['红球1'], latest_row['红球2'], latest_row['红球3'],
                         latest_row['红球4'], latest_row['红球5'], latest_row['红球6']]
            latest_blue = latest_row['蓝球']

            red_migration, blue_migration = self.core_algorithms.markov_chain_algorithm(
                latest_red, latest_blue, red_hist_prob, blue_hist_prob,
                red_follow_prob, blue_follow_prob
            )

            # 保存迁移概率
            red_migration.to_excel(writer, sheet_name='红球号码迁移概率', index=False)
            blue_migration.to_excel(writer, sheet_name='蓝球号码迁移概率', index=False)
