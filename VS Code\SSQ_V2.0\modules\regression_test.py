#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回归测试模块
实现回归测试功能，包括循环运行、比对分析和结果保存
"""

import pandas as pd
import os
import tkinter as tk
from tkinter import filedialog
from datetime import datetime
from typing import Dict, List, Any, Tuple, Set
from .core_algorithms import CoreAlgorithms
from .utils import Utils
from .data_reader import DataReader


class RegressionTest:
    """回归测试类"""
    
    def __init__(self, ssq_history: pd.DataFrame, parameters: Dict[str, Any]):
        """
        初始化回归测试
        
        Args:
            ssq_history: SSQ历史数据
            parameters: 参数配置
        """
        self.ssq_history = ssq_history
        self.parameters = parameters
        self.core_algorithms = CoreAlgorithms()
        self.utils = Utils()
        self.data_reader = DataReader()
        self.test_results = []
    
    def run_regression_test(self, start_period: str, missing_range: int) -> bool:
        """
        运行回归测试
        
        Args:
            start_period: 起始期号
            missing_range: 遗漏期数据库范围
            
        Returns:
            bool: 是否成功
        """
        try:
            print(f"开始回归测试：起始期号 {start_period}，遗漏期范围 {missing_range}")
            
            # 选择统计分析文件
            analysis_file = self._select_analysis_file()
            if not analysis_file:
                print("未选择统计分析文件")
                return False
            
            # 读取统计分析数据
            analysis_data = self.data_reader.read_analysis_file(analysis_file)
            
            # 统计排序序号次数并生成表格
            ranking_stats = self._calculate_ranking_statistics(analysis_data)
            
            # 获取测试期号列表
            test_periods = self._get_test_periods(start_period)
            
            if not test_periods:
                print("没有找到需要测试的期号")
                return False
            
            print(f"需要循环运行的总期数：{len(test_periods)}")
            
            # 循环运行测试
            for i, period in enumerate(test_periods):
                if (i + 1) % 100 == 0:
                    current_row = self.ssq_history[self.ssq_history['期号'] == int(period)]
                    if not current_row.empty:
                        current_row = current_row.iloc[0]
                        red_balls = [current_row['红球1'], current_row['红球2'], current_row['红球3'],
                                   current_row['红球4'], current_row['红球5'], current_row['红球6']]
                        blue_ball = current_row['蓝球']
                        balls_str = self.utils.format_balls_display(red_balls, blue_ball)
                        print(f"已完成 {i + 1} 期，当前期号：{period}，红蓝球号码：{balls_str}")
                
                self._test_single_period(period, missing_range, ranking_stats)
            
            # 保存结果
            success = self._save_test_results(start_period, missing_range)
            
            if success:
                print(f"回归测试完成！共测试 {len(test_periods)} 期数据")
                return True
            else:
                print("保存结果失败")
                return False
                
        except Exception as e:
            print(f"回归测试过程中发生错误：{str(e)}")
            return False
    
    def _select_analysis_file(self) -> str:
        """
        选择统计分析文件
        
        Returns:
            str: 选择的文件路径，如果取消则返回空字符串
        """
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()
            
            # 设置文件对话框的初始目录
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Output")
            
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择SSQ统计分析文件",
                initialdir=output_dir,
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            root.destroy()
            return file_path
            
        except Exception as e:
            print(f"文件选择失败：{str(e)}")
            return ""
    
    def _calculate_ranking_statistics(self, analysis_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        统计排序序号次数并生成表格
        
        Args:
            analysis_data: 统计分析数据
            
        Returns:
            Dict[str, Any]: 排序统计结果
        """
        # 获取历史出现概率排序数据
        hist_data = analysis_data['历史出现概率排序']
        
        # 统计红球排序序号出现次数
        red_ranking_counts = {}
        for i in range(1, 34):
            red_ranking_counts[i] = 0
        
        # 统计蓝球排序序号出现次数
        blue_ranking_counts = {}
        for i in range(1, 17):
            blue_ranking_counts[i] = 0
        
        # 遍历数据统计
        red_cols = ['红球1排序', '红球2排序', '红球3排序', '红球4排序', '红球5排序', '红球6排序']
        for _, row in hist_data.iterrows():
            for col in red_cols:
                ranking = int(row[col])
                if 1 <= ranking <= 33:
                    red_ranking_counts[ranking] += 1
            
            blue_ranking = int(row['蓝球排序'])
            if 1 <= blue_ranking <= 16:
                blue_ranking_counts[blue_ranking] += 1
        
        # 生成排序表格并获取前N项
        red_ranking_table = self._create_ranking_table(red_ranking_counts)
        blue_ranking_table = self._create_ranking_table(blue_ranking_counts)
        
        # 获取前N项排序序号
        history_num_red = self.parameters['HistoryNum_Red']
        history_num_blue = self.parameters['HistoryNum_Blue']
        
        HXR = red_ranking_table.head(history_num_red)['排序序号'].tolist()
        HXB = blue_ranking_table.head(history_num_blue)['排序序号'].tolist()
        
        # 对号码迁移概率排序做同样处理
        migration_data = analysis_data['号码迁移概率排序']
        
        # 统计迁移概率排序序号出现次数
        red_migration_counts = {}
        for i in range(1, 34):
            red_migration_counts[i] = 0
        
        blue_migration_counts = {}
        for i in range(1, 17):
            blue_migration_counts[i] = 0
        
        for _, row in migration_data.iterrows():
            for col in red_cols:
                ranking = int(row[col])
                if 1 <= ranking <= 33:
                    red_migration_counts[ranking] += 1
            
            blue_ranking = int(row['蓝球排序'])
            if 1 <= blue_ranking <= 16:
                blue_migration_counts[blue_ranking] += 1
        
        red_migration_table = self._create_ranking_table(red_migration_counts)
        blue_migration_table = self._create_ranking_table(blue_migration_counts)
        
        follow_num_red = self.parameters['FollowNum_Red']
        follow_num_blue = self.parameters['FollowNum_Blue']
        
        FXR = red_migration_table.head(follow_num_red)['排序序号'].tolist()
        FXB = blue_migration_table.head(follow_num_blue)['排序序号'].tolist()
        
        return {
            'HXR': HXR,
            'HXB': HXB,
            'FXR': FXR,
            'FXB': FXB
        }
    
    def _create_ranking_table(self, ranking_counts: Dict[int, int]) -> pd.DataFrame:
        """
        创建排序表格
        
        Args:
            ranking_counts: 排序序号出现次数字典
            
        Returns:
            pd.DataFrame: 排序表格
        """
        data = []
        for ranking, count in ranking_counts.items():
            data.append({'排序序号': ranking, '出现次数': count})
        
        df = pd.DataFrame(data)
        # 按出现次数从多到少排序，次数相等时按序号从小到大排序
        df = df.sort_values(['出现次数', '排序序号'], ascending=[False, True]).reset_index(drop=True)
        
        return df
    
    def _get_test_periods(self, start_period: str) -> List[str]:
        """
        获取需要测试的期号列表
        
        Args:
            start_period: 起始期号
            
        Returns:
            List[str]: 期号列表
        """
        start_int = int(start_period)
        
        # 获取所有期号
        all_periods = sorted(self.ssq_history['期号'].tolist())
        
        # 找到起始期号的位置
        start_index = None
        for i, period in enumerate(all_periods):
            if period == start_int:
                start_index = i
                break
        
        if start_index is None:
            return []
        
        # 返回起始期号之后的所有期号
        return [str(period) for period in all_periods[start_index:]]

    def _test_single_period(self, period: str, missing_range: int, ranking_stats: Dict[str, Any]):
        """
        测试单个期号

        Args:
            period: 期号
            missing_range: 遗漏期范围
            ranking_stats: 排序统计结果
        """
        period_int = int(period)

        # 获取当前期的实际红蓝球号码
        current_row = self.ssq_history[self.ssq_history['期号'] == period_int]
        if current_row.empty:
            return

        current_row = current_row.iloc[0]
        actual_red = [current_row['红球1'], current_row['红球2'], current_row['红球3'],
                     current_row['红球4'], current_row['红球5'], current_row['红球6']]
        actual_blue = current_row['蓝球']

        # 构建数据库
        databases = self._build_databases_for_period(period, missing_range)

        # 初选号码并分析比对
        candidate_groups = self._initial_selection_and_compare(databases, ranking_stats,
                                                             actual_red, actual_blue)

        # 保存测试结果
        result = {
            '期号': period,
            '实际红球': actual_red,
            '实际蓝球': actual_blue,
            '候选组': candidate_groups
        }

        self.test_results.append(result)

    def _build_databases_for_period(self, period: str, missing_range: int) -> Dict[str, pd.DataFrame]:
        """
        为指定期号构建数据库

        Args:
            period: 期号
            missing_range: 遗漏期范围

        Returns:
            Dict[str, pd.DataFrame]: 各种数据库
        """
        period_int = int(period)
        databases = {}

        # 遗漏期数据库：当前期之前的missing_range期
        missing_db = self.ssq_history[self.ssq_history['期号'] < period_int].tail(missing_range)

        # 历史概率数据库：当前期之前的DatabaseScope_history期
        hist_scope = self.parameters['DatabaseScope_history']
        hist_db = self.ssq_history[self.ssq_history['期号'] < period_int].tail(hist_scope)

        # 马尔科夫数据库：当前期之前的DatabaseScope_follow期
        follow_scope = self.parameters['DatabaseScope_follow']
        markov_db = self.ssq_history[self.ssq_history['期号'] < period_int].tail(follow_scope)

        databases['missing'] = missing_db
        databases['history'] = hist_db
        databases['markov'] = markov_db

        return databases

    def _initial_selection_and_compare(self, databases: Dict[str, pd.DataFrame],
                                     ranking_stats: Dict[str, Any],
                                     actual_red: List[int], actual_blue: int) -> Dict[str, Any]:
        """
        初选号码并分析比对

        Args:
            databases: 各种数据库
            ranking_stats: 排序统计结果
            actual_red: 实际红球
            actual_blue: 实际蓝球

        Returns:
            Dict[str, Any]: 候选组比对结果
        """
        groups = {}

        # 第1组：基于遗漏期数据库的去重复号码
        missing_db = databases['missing']
        red_set1 = set()
        blue_set1 = set()

        for _, row in missing_db.iterrows():
            red_balls = [row['红球1'], row['红球2'], row['红球3'],
                        row['红球4'], row['红球5'], row['红球6']]
            red_set1.update(red_balls)
            blue_set1.add(row['蓝球'])

        # 计算第1组命中情况
        red_hits1 = len(set(actual_red).intersection(red_set1))
        blue_hits1 = 1 if actual_blue in blue_set1 else 0
        max_hits1 = red_hits1 + blue_hits1

        groups['group1'] = {
            'red_balls': list(red_set1),
            'blue_balls': list(blue_set1),
            'red_count': len(red_set1),
            'blue_count': len(blue_set1),
            'max_hits': max_hits1
        }

        # 第2组：基于历史出现概率的选择
        hist_db = databases['history']
        red_hist_prob, blue_hist_prob = self.core_algorithms.calculate_historical_probability(hist_db)

        # 排序概率
        red_hist_sorted = self.core_algorithms.sort_by_probability(red_hist_prob)
        blue_hist_sorted = self.core_algorithms.sort_by_probability(blue_hist_prob)

        # 根据HXR和HXB选择号码
        HXR = ranking_stats['HXR']
        HXB = ranking_stats['HXB']

        red_set2 = set()
        for rank in HXR:
            if rank <= len(red_hist_sorted):
                ball = red_hist_sorted.iloc[rank - 1]['号码']
                red_set2.add(ball)

        blue_set2 = set()
        for rank in HXB:
            if rank <= len(blue_hist_sorted):
                ball = blue_hist_sorted.iloc[rank - 1]['号码']
                blue_set2.add(ball)

        # 计算第2组命中情况
        red_hits2 = len(set(actual_red).intersection(red_set2))
        blue_hits2 = 1 if actual_blue in blue_set2 else 0
        max_hits2 = red_hits2 + blue_hits2

        groups['group2'] = {
            'red_balls': list(red_set2),
            'blue_balls': list(blue_set2),
            'red_count': len(red_set2),
            'blue_count': len(blue_set2),
            'max_hits': max_hits2
        }

        # 第3组：基于马尔科夫链算法的选择
        markov_db = databases['markov']

        if not markov_db.empty:
            # 获取最新一期数据
            latest_row = markov_db.iloc[-1]
            latest_red = [latest_row['红球1'], latest_row['红球2'], latest_row['红球3'],
                         latest_row['红球4'], latest_row['红球5'], latest_row['红球6']]
            latest_blue = latest_row['蓝球']

            # 计算跟随概率
            red_follow_prob, blue_follow_prob = self.core_algorithms.calculate_following_probability(markov_db)

            # 马尔科夫链算法
            red_migration, blue_migration = self.core_algorithms.markov_chain_algorithm(
                latest_red, latest_blue, red_hist_prob, blue_hist_prob,
                red_follow_prob, blue_follow_prob
            )

            # 排序迁移概率
            red_migration_sorted = self.core_algorithms.sort_by_probability(red_migration)
            blue_migration_sorted = self.core_algorithms.sort_by_probability(blue_migration)

            # 根据FXR和FXB选择号码
            FXR = ranking_stats['FXR']
            FXB = ranking_stats['FXB']

            red_set3 = set()
            for rank in FXR:
                if rank <= len(red_migration_sorted):
                    ball = red_migration_sorted.iloc[rank - 1]['号码']
                    red_set3.add(ball)

            blue_set3 = set()
            for rank in FXB:
                if rank <= len(blue_migration_sorted):
                    ball = blue_migration_sorted.iloc[rank - 1]['号码']
                    blue_set3.add(ball)

            # 计算第3组命中情况
            red_hits3 = len(set(actual_red).intersection(red_set3))
            blue_hits3 = 1 if actual_blue in blue_set3 else 0
            max_hits3 = red_hits3 + blue_hits3

            groups['group3'] = {
                'red_balls': list(red_set3),
                'blue_balls': list(blue_set3),
                'red_count': len(red_set3),
                'blue_count': len(blue_set3),
                'max_hits': max_hits3
            }
        else:
            groups['group3'] = {
                'red_balls': [],
                'blue_balls': [],
                'red_count': 0,
                'blue_count': 0,
                'max_hits': 0
            }

        # 第4组：求交集
        red_intersection = red_set1
        blue_intersection = blue_set1

        if 'group2' in groups:
            red_intersection = red_intersection.intersection(red_set2)
            blue_intersection = blue_intersection.intersection(blue_set2)

        if 'group3' in groups and groups['group3']['red_count'] > 0:
            red_intersection = red_intersection.intersection(red_set3)
            blue_intersection = blue_intersection.intersection(blue_set3)

        # 如果蓝球交集为空，设为0
        if not blue_intersection:
            blue_intersection = {0}

        # 计算第4组命中情况
        red_hits4 = len(set(actual_red).intersection(red_intersection))
        blue_hits4 = 1 if actual_blue in blue_intersection else 0
        max_hits4 = red_hits4 + blue_hits4

        groups['group4'] = {
            'red_balls': list(red_intersection),
            'blue_balls': list(blue_intersection),
            'red_count': len(red_intersection),
            'blue_count': len(blue_intersection),
            'max_hits': max_hits4
        }

        return groups

    def _save_test_results(self, start_period: str, missing_range: int) -> bool:
        """
        保存回归测试结果

        Args:
            start_period: 起始期号
            missing_range: 遗漏期范围

        Returns:
            bool: 是否成功
        """
        try:
            # 生成文件名
            latest_period = str(self.ssq_history['期号'].max())
            date_str = datetime.now().strftime("%Y%m%d")

            filename = (f"回归测试SSQ_{date_str}_{latest_period}_"
                       f"{self.parameters['DatabaseScope_history']}_"
                       f"{self.parameters['DatabaseScope_follow']}_{missing_range}_"
                       f"序号截止_中奖.xlsx")

            # 确保Output文件夹存在
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Output")
            os.makedirs(output_dir, exist_ok=True)

            file_path = os.path.join(output_dir, filename)

            # 创建Excel写入器
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 保存详细结果
                self._save_detailed_test_results(writer)

            print(f"回归测试结果已保存到：{file_path}")
            return True

        except Exception as e:
            print(f"保存回归测试结果失败：{str(e)}")
            return False

    def _save_detailed_test_results(self, writer):
        """保存详细测试结果"""
        data = []

        for result in self.test_results:
            period = result['期号']
            actual_red = result['实际红球']
            actual_blue = result['实际蓝球']
            candidate_groups = result['候选组']

            # 格式化实际红蓝球号码
            actual_red_str = ','.join([str(ball) for ball in sorted(actual_red)])
            actual_blue_str = str(actual_blue)

            # 处理4组候选号码
            for group_name in ['group1', 'group2', 'group3', 'group4']:
                if group_name in candidate_groups:
                    group_data = candidate_groups[group_name]

                    # 格式化红球号码
                    red_balls = group_data['red_balls']
                    if red_balls:
                        red_str = ','.join([str(ball) for ball in sorted(red_balls)])
                    else:
                        red_str = ""

                    # 格式化蓝球号码
                    blue_balls = group_data['blue_balls']
                    if blue_balls and 0 not in blue_balls:
                        blue_str = ','.join([str(ball) for ball in sorted(blue_balls)])
                    else:
                        blue_str = "0"

                    # 添加数据行
                    row = [
                        period,
                        actual_red_str,
                        actual_blue_str,
                        red_str,
                        blue_str,
                        group_data['red_count'],
                        group_data['blue_count'],
                        group_data['max_hits']
                    ]

                    data.append(row)

        # 创建列名
        columns = [
            '分析期号',
            '实际红球号码',
            '实际蓝球号码',
            '复式红球号码',
            '复式蓝球号码',
            '红球号码个数',
            '蓝球号码个数',
            '最大命中情况'
        ]

        df = pd.DataFrame(data, columns=columns)
        df.to_excel(writer, sheet_name='详细结果', index=False)
