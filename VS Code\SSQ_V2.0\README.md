# SSQ历史数据分析系统 v2.0

基于SSQ历史数据的统计分析、预测选号与回归测试程序。

## 功能特性

- **统计分析**：基于历史数据进行概率统计和特性分析
- **预测选号**：使用马尔科夫链算法进行号码预测
- **回归测试**：验证预测算法的准确性

## 系统要求

- Python 3.7+
- Windows操作系统（支持tkinter文件对话框）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 数据文件准备

确保根目录下有以下文件：
- `lottery_data_all.xlsx`：包含SSQ历史数据和参数配置

### Excel文件结构

#### SSQ_data_all标签页
- A列：期号
- I-N列：红球1-6
- O列：蓝球

#### Parameters标签页
- B2-C9：各种参数配置

## 使用方法

1. 运行主程序：
```bash
python main.py
```

2. 选择功能：
   - 1：统计分析
   - 2：预测选号
   - 3：回归测试
   - 0：退出程序

### 统计分析

1. 输入起始期号（如：23001）
2. 输入目标期号（输入0表示到最新期）
3. 程序将分析指定范围内的数据并保存结果

### 预测选号

1. 确认已完成统计分析
2. 输入目标期号（输入0表示预测下一期）
3. 输入遗漏期数据库范围
4. 选择统计分析文件
5. 程序将生成预测号码并保存结果

### 回归测试

1. 确认已完成统计分析
2. 输入起始期号
3. 输入遗漏期数据库范围
4. 选择统计分析文件
5. 程序将进行回归测试并保存结果

## 输出文件

所有结果文件保存在`Output`文件夹中：

- 统计分析：`统计分析SSQ_日期_期号_历史范围_跟随范围.xlsx`
- 预测选号：`筛选号码SSQ_日期_期号_历史范围_跟随范围_遗漏范围.xlsx`
- 回归测试：`回归测试SSQ_日期_期号_历史范围_跟随范围_遗漏范围_序号截止_中奖.xlsx`

## 核心算法

### 统计历史出现概率
统计每个红蓝球号码在历史数据中的出现频率。

### 马尔科夫链算法
基于最新一期的红蓝球号码，计算下一期各号码的迁移概率。

### 特性分析
- 大小球特性：红球>16为大球，蓝球>8为大球
- 奇偶特性：号码除以2不能整除为奇球
- 质合特性：质数（包括1）为质球
- 和值：红球号码之和
- AC值：号码数字复杂度

## 注意事项

1. 确保数据文件格式正确
2. 期号格式为4位数或5位数
3. 程序不会自动删除或修改原始数据文件
4. 建议定期备份Output文件夹中的结果

## 项目结构

```
SSQ_V2.0/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 说明文档
├── lottery_data_all.xlsx  # 数据文件
├── Output/                # 输出文件夹
└── modules/               # 功能模块
    ├── __init__.py
    ├── data_reader.py     # 数据读取模块
    ├── utils.py           # 工具模块
    ├── core_algorithms.py # 核心算法模块
    ├── statistical_analysis.py # 统计分析模块
    ├── prediction.py      # 预测选号模块
    └── regression_test.py # 回归测试模块
```

## 版本信息

- 版本：2.0
- 开发者：AI Assistant
- 开发日期：2025-08-28

## 更新日志

### v2.0.1 (2025-08-28)
- 修复统计分析中遗漏值计算逻辑，现在正确基于历史概率算法数据库计算
- 优化预测选号和回归测试中的初选号码逻辑，确保按排序序号正确选择号码
- 增强预测选号功能的信息输出，显示各组复式红蓝球号码统计
- 完善代码注释和错误处理
