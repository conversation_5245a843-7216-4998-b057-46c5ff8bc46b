#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心算法模块
实现统计历史出现概率、统计历史跟随性概率、马尔科夫链算法等核心算法
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
from .utils import Utils


class CoreAlgorithms:
    """核心算法类"""
    
    def __init__(self):
        """初始化核心算法"""
        self.utils = Utils()
    
    def calculate_historical_probability(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        统计历史出现概率
        
        Args:
            data: 历史数据
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (红球概率表, 蓝球概率表)
        """
        # 统计红球出现次数
        red_counts = {}
        for i in range(1, 34):  # 红球1-33
            red_counts[i] = 0
        
        # 统计蓝球出现次数
        blue_counts = {}
        for i in range(1, 17):  # 蓝球1-16
            blue_counts[i] = 0
        
        # 遍历历史数据统计
        for _, row in data.iterrows():
            # 统计红球
            for col in ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']:
                ball = int(row[col])
                red_counts[ball] += 1
            
            # 统计蓝球
            blue_ball = int(row['蓝球'])
            blue_counts[blue_ball] += 1
        
        # 计算红球概率
        total_red = len(data) * 6  # 总红球数
        red_prob_data = []
        for ball in range(1, 34):
            probability = red_counts[ball] / total_red
            red_prob_data.append({'号码': ball, '概率': probability})
        
        red_prob_df = pd.DataFrame(red_prob_data)
        
        # 计算蓝球概率
        total_blue = len(data)  # 总蓝球数
        blue_prob_data = []
        for ball in range(1, 17):
            probability = blue_counts[ball] / total_blue
            blue_prob_data.append({'号码': ball, '概率': probability})
        
        blue_prob_df = pd.DataFrame(blue_prob_data)
        
        return red_prob_df, blue_prob_df
    
    def calculate_following_probability(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        统计历史跟随性概率
        
        Args:
            data: 历史数据
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: (红球跟随概率矩阵33x33, 蓝球跟随概率矩阵16x16)
        """
        # 初始化跟随次数矩阵
        red_follow_counts = np.zeros((33, 33))  # 33x33矩阵
        blue_follow_counts = np.zeros((16, 16))  # 16x16矩阵
        
        # 遍历相邻两期数据
        for i in range(len(data) - 1):
            current_row = data.iloc[i]
            next_row = data.iloc[i + 1]
            
            # 获取当前期和下一期的红球
            current_red = [current_row['红球1'], current_row['红球2'], current_row['红球3'],
                          current_row['红球4'], current_row['红球5'], current_row['红球6']]
            next_red = [next_row['红球1'], next_row['红球2'], next_row['红球3'],
                       next_row['红球4'], next_row['红球5'], next_row['红球6']]
            
            # 统计红球跟随关系
            for curr_ball in current_red:
                for next_ball in next_red:
                    red_follow_counts[curr_ball - 1][next_ball - 1] += 1
            
            # 统计蓝球跟随关系
            current_blue = current_row['蓝球']
            next_blue = next_row['蓝球']
            blue_follow_counts[current_blue - 1][next_blue - 1] += 1
        
        # 转换为概率矩阵
        red_follow_prob = np.zeros((33, 33))
        for i in range(33):
            row_sum = np.sum(red_follow_counts[i])
            if row_sum > 0:
                red_follow_prob[i] = red_follow_counts[i] / row_sum
        
        blue_follow_prob = np.zeros((16, 16))
        for i in range(16):
            row_sum = np.sum(blue_follow_counts[i])
            if row_sum > 0:
                blue_follow_prob[i] = blue_follow_counts[i] / row_sum
        
        return red_follow_prob, blue_follow_prob
    
    def markov_chain_algorithm(self, latest_balls: List[int], latest_blue: int,
                              red_hist_prob: pd.DataFrame, blue_hist_prob: pd.DataFrame,
                              red_follow_prob: np.ndarray, blue_follow_prob: np.ndarray) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        马尔科夫链算法
        
        Args:
            latest_balls: 最新一期红球号码列表
            latest_blue: 最新一期蓝球号码
            red_hist_prob: 红球历史概率表
            blue_hist_prob: 蓝球历史概率表
            red_follow_prob: 红球跟随概率矩阵
            blue_follow_prob: 蓝球跟随概率矩阵
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (红球迁移概率表, 蓝球迁移概率表)
        """
        # 红球马尔科夫链计算
        # 构建SA矩阵：从跟随概率矩阵中提取最新红球相关的列
        SA = np.zeros((33, 6))
        for i, ball in enumerate(latest_balls):
            SA[:, i] = red_follow_prob[:, ball - 1]
        
        # 构建SB向量：从历史概率中提取最新红球相关的概率
        SB = np.zeros(6)
        for i, ball in enumerate(latest_balls):
            prob_row = red_hist_prob[red_hist_prob['号码'] == ball]
            if not prob_row.empty:
                SB[i] = prob_row.iloc[0]['概率']
        
        # 计算红球迁移概率向量
        red_migration_prob = SA @ SB
        
        # 构建红球迁移概率表
        red_migration_data = []
        for i in range(33):
            red_migration_data.append({'号码': i + 1, '概率': red_migration_prob[i]})
        red_migration_df = pd.DataFrame(red_migration_data)
        
        # 蓝球马尔科夫链计算
        # 直接使用跟随概率矩阵中最新蓝球对应的列
        blue_migration_prob = blue_follow_prob[:, latest_blue - 1]
        
        # 构建蓝球迁移概率表
        blue_migration_data = []
        for i in range(16):
            blue_migration_data.append({'号码': i + 1, '概率': blue_migration_prob[i]})
        blue_migration_df = pd.DataFrame(blue_migration_data)
        
        return red_migration_df, blue_migration_df
    
    def sort_by_probability(self, prob_df: pd.DataFrame) -> pd.DataFrame:
        """
        按概率排序
        
        Args:
            prob_df: 概率表
            
        Returns:
            pd.DataFrame: 排序后的概率表
        """
        # 按概率从大到小排序，概率相等时按号码从小到大排序
        sorted_df = prob_df.sort_values(['概率', '号码'], ascending=[False, True]).reset_index(drop=True)
        
        # 添加排序序号
        sorted_df['排序'] = range(1, len(sorted_df) + 1)
        
        return sorted_df
    
    def get_ranking_in_sorted_table(self, balls: List[int], sorted_table: pd.DataFrame) -> List[int]:
        """
        获取号码在排序表中的排序序号
        
        Args:
            balls: 号码列表
            sorted_table: 排序后的概率表
            
        Returns:
            List[int]: 排序序号列表
        """
        rankings = []
        for ball in balls:
            rank_row = sorted_table[sorted_table['号码'] == ball]
            if not rank_row.empty:
                rankings.append(rank_row.iloc[0]['排序'])
            else:
                rankings.append(0)  # 如果找不到，返回0
        
        return rankings
    
    def analyze_ball_characteristics(self, red_balls: List[int], blue_ball: int,
                                   missing_values: List[int] = None) -> Dict[str, Any]:
        """
        分析号码特性
        
        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
            missing_values: 遗漏值列表（可选）
            
        Returns:
            Dict[str, Any]: 特性分析结果
        """
        characteristics = {}
        
        # 红球大小特性
        big_count = sum(1 for ball in red_balls if self.utils.is_big_ball(ball, False))
        characteristics['红球大球个数'] = big_count
        
        # 红球奇偶特性
        odd_count = sum(1 for ball in red_balls if self.utils.is_odd_ball(ball))
        characteristics['红球奇球个数'] = odd_count
        
        # 红球质合特性
        prime_count = sum(1 for ball in red_balls if self.utils.is_prime_ball(ball))
        characteristics['红球质球个数'] = prime_count
        
        # 红球和值
        sum_value = self.utils.calculate_sum(red_balls)
        characteristics['红球和值'] = sum_value
        
        # 红球AC值
        ac_value = self.utils.calculate_ac_value(sorted(red_balls))
        characteristics['红球AC值'] = ac_value
        
        # 遗漏值（如果提供）
        if missing_values:
            characteristics['遗漏值'] = missing_values
        
        return characteristics
