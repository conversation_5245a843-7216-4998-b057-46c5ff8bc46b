#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据读取模块
负责读取Excel文件中的SSQ历史数据和参数配置
"""

import pandas as pd
import os
from typing import Dict, List, Any


class DataReader:
    """数据读取器类"""
    
    def __init__(self):
        """初始化数据读取器"""
        self.data_file = "lottery_data_all.xlsx"
        self.base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.file_path = os.path.join(self.base_path, self.data_file)
    
    def read_ssq_history(self) -> pd.DataFrame:
        """
        读取SSQ历史数据
        
        Returns:
            pd.DataFrame: SSQ历史数据，包含期号和红蓝球号码
        """
        try:
            # 读取SSQ_data_all标签页的A列至O列数据
            df = pd.read_excel(self.file_path, sheet_name='SSQ_data_all', usecols='A,I:O')
            
            # 重命名列
            columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
            df.columns = columns
            
            # 清除无效数据和空数据行
            df = df.dropna()
            df = df[df['期号'] != '']
            
            # 确保期号为整数类型
            df['期号'] = df['期号'].astype(int)
            
            # 确保红蓝球号码为整数类型
            for col in columns[1:]:
                df[col] = df[col].astype(int)
            
            # 按期号从小到大排序
            df = df.sort_values('期号').reset_index(drop=True)
            
            print(f"成功读取SSQ历史数据：{len(df)}期")
            return df
            
        except Exception as e:
            raise Exception(f"读取SSQ历史数据失败：{str(e)}")
    
    def read_parameters(self) -> Dict[str, Any]:
        """
        读取参数配置
        
        Returns:
            Dict[str, Any]: 参数配置字典
        """
        try:
            # 读取Parameters标签页的B2至C9数据
            df = pd.read_excel(self.file_path, sheet_name='Parameters', 
                             usecols='B:C', skiprows=1, nrows=8, header=None)
            
            # 参数映射
            param_names = [
                ('NumRB_min', 'NumRB_max'),      # B2, C2
                ('NumRQ_min', 'NumRQ_max'),      # B3, C3
                ('NumRZ_min', 'NumRZ_max'),      # B4, C4
                ('SumR_min', 'SumR_max'),        # B5, C5
                ('NumRAC_min', 'NumRAC_max'),    # B6, C6
                ('DatabaseScope_history', 'DatabaseScope_follow'),  # B7, C7
                ('HistoryNum_Red', 'HistoryNum_Blue'),              # B8, C8
                ('FollowNum_Red', 'FollowNum_Blue')                 # B9, C9
            ]
            
            parameters = {}
            for i, (param1, param2) in enumerate(param_names):
                parameters[param1] = int(df.iloc[i, 0])
                parameters[param2] = int(df.iloc[i, 1])
            
            print("成功读取参数配置")
            return parameters
            
        except Exception as e:
            raise Exception(f"读取参数配置失败：{str(e)}")
    
    def read_analysis_file(self, file_path: str) -> Dict[str, pd.DataFrame]:
        """
        读取统计分析结果文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, pd.DataFrame]: 各标签页的数据
        """
        try:
            result = {}
            
            # 读取历史出现概率排序标签页
            df_history = pd.read_excel(file_path, sheet_name='历史出现概率排序', usecols='I:O')
            result['历史出现概率排序'] = df_history
            
            # 读取号码迁移概率排序标签页
            df_migration = pd.read_excel(file_path, sheet_name='号码迁移概率排序', usecols='I:O')
            result['号码迁移概率排序'] = df_migration
            
            return result
            
        except Exception as e:
            raise Exception(f"读取分析文件失败：{str(e)}")
    
    def validate_file_exists(self) -> bool:
        """
        验证数据文件是否存在
        
        Returns:
            bool: 文件是否存在
        """
        return os.path.exists(self.file_path)
    
    def get_file_info(self) -> Dict[str, Any]:
        """
        获取数据文件信息
        
        Returns:
            Dict[str, Any]: 文件信息
        """
        if not self.validate_file_exists():
            return {"exists": False}
        
        stat = os.stat(self.file_path)
        return {
            "exists": True,
            "size": stat.st_size,
            "modified": stat.st_mtime,
            "path": self.file_path
        }
