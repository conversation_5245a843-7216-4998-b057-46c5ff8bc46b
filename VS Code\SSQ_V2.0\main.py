#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSQ历史数据统计分析、预测选号与回归测试程序
主程序入口文件

作者：AI Assistant
版本：2.0
日期：2025-08-28
"""

import sys
import os
from datetime import datetime

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入各功能模块
from modules.data_reader import DataReader
from modules.statistical_analysis import StatisticalAnalysis
from modules.prediction import Prediction
from modules.regression_test import RegressionTest
from modules.utils import Utils


class SSQAnalyzer:
    """SSQ分析器主类"""
    
    def __init__(self):
        """初始化SSQ分析器"""
        self.data_reader = DataReader()
        self.utils = Utils()
        self.ssq_history = None
        self.parameters = None
        
    def initialize(self):
        """初始化程序，读取默认数据"""
        try:
            print("正在初始化程序...")
            print("正在读取默认数据文件...")
            
            # 读取原始数据和参数
            self.ssq_history = self.data_reader.read_ssq_history()
            self.parameters = self.data_reader.read_parameters()
            
            print(f"成功读取 {len(self.ssq_history)} 期SSQ历史数据")
            print("参数配置读取完成")
            print("程序初始化完成！\n")
            return True
            
        except Exception as e:
            print(f"程序初始化失败：{str(e)}")
            return False
    
    def show_menu(self):
        """显示主菜单"""
        print("=" * 50)
        print("SSQ历史数据分析系统 v2.0")
        print("=" * 50)
        print("请选择功能：")
        print("1. 统计分析")
        print("2. 预测选号")
        print("3. 回归测试")
        print("0. 退出程序")
        print("=" * 50)
    
    def get_user_choice(self):
        """获取用户选择"""
        while True:
            try:
                choice = input("请输入您的选择 (0-3): ").strip()
                if choice in ['0', '1', '2', '3']:
                    return choice
                else:
                    print("无效选择，请输入 0-3 之间的数字")
            except KeyboardInterrupt:
                print("\n程序被用户中断")
                return '0'
            except Exception as e:
                print(f"输入错误：{str(e)}")
    
    def run_statistical_analysis(self):
        """运行统计分析功能"""
        print("\n" + "=" * 30)
        print("统计分析功能")
        print("=" * 30)
        
        try:
            # 创建统计分析实例
            analysis = StatisticalAnalysis(self.ssq_history, self.parameters)
            
            # 获取用户输入的参数
            start_period = input("请输入起始期号（如：23001）: ").strip()
            target_period = input("请输入目标期号（输入0表示到最新期）: ").strip()
            
            # 验证期号格式
            if not self.utils.validate_period_format(start_period):
                print("起始期号格式错误！")
                return
            
            if target_period != '0' and not self.utils.validate_period_format(target_period):
                print("目标期号格式错误！")
                return
            
            # 运行统计分析
            result = analysis.run_analysis(start_period, target_period)
            
            if result:
                print("统计分析完成！结果已保存到Output文件夹")
            else:
                print("统计分析失败！")
                
        except Exception as e:
            print(f"统计分析过程中发生错误：{str(e)}")
    
    def run_prediction(self):
        """运行预测选号功能"""
        print("\n" + "=" * 30)
        print("预测选号功能")
        print("=" * 30)
        
        # 检查是否已完成统计分析
        completed = input("您是否已完成统计分析运算？(y/n): ").strip().lower()
        if completed != 'y':
            print("请先完成统计分析运算！")
            return
        
        try:
            # 创建预测实例
            prediction = Prediction(self.ssq_history, self.parameters)
            
            # 获取用户输入的参数
            target_period = input("请输入目标期号（输入0表示预测下一期）: ").strip()
            missing_range = input("请输入遗漏期数据库范围: ").strip()
            
            # 验证输入
            if target_period != '0' and not self.utils.validate_period_format(target_period):
                print("目标期号格式错误！")
                return
            
            try:
                missing_range = int(missing_range)
                if missing_range <= 0:
                    print("遗漏期数据库范围必须为正整数！")
                    return
            except ValueError:
                print("遗漏期数据库范围必须为整数！")
                return
            
            # 运行预测选号
            result = prediction.run_prediction(target_period, missing_range)
            
            if result:
                print("预测选号完成！结果已保存到Output文件夹")
            else:
                print("预测选号失败！")
                
        except Exception as e:
            print(f"预测选号过程中发生错误：{str(e)}")
    
    def run_regression_test(self):
        """运行回归测试功能"""
        print("\n" + "=" * 30)
        print("回归测试功能")
        print("=" * 30)
        
        # 检查是否已完成统计分析
        completed = input("您是否已完成统计分析运算？(y/n): ").strip().lower()
        if completed != 'y':
            print("请先完成统计分析运算！")
            return
        
        try:
            # 创建回归测试实例
            regression = RegressionTest(self.ssq_history, self.parameters)
            
            # 获取用户输入的参数
            start_period = input("请输入起始期号（如：23001）: ").strip()
            missing_range = input("请输入遗漏期数据库范围: ").strip()
            
            # 验证输入
            if not self.utils.validate_period_format(start_period):
                print("起始期号格式错误！")
                return
            
            try:
                missing_range = int(missing_range)
                if missing_range <= 0:
                    print("遗漏期数据库范围必须为正整数！")
                    return
            except ValueError:
                print("遗漏期数据库范围必须为整数！")
                return
            
            # 运行回归测试
            result = regression.run_regression_test(start_period, missing_range)
            
            if result:
                print("回归测试完成！结果已保存到Output文件夹")
            else:
                print("回归测试失败！")
                
        except Exception as e:
            print(f"回归测试过程中发生错误：{str(e)}")
    
    def run(self):
        """运行主程序"""
        # 初始化程序
        if not self.initialize():
            return
        
        # 主循环
        while True:
            self.show_menu()
            choice = self.get_user_choice()
            
            if choice == '0':
                print("感谢使用SSQ分析系统，再见！")
                break
            elif choice == '1':
                self.run_statistical_analysis()
            elif choice == '2':
                self.run_prediction()
            elif choice == '3':
                self.run_regression_test()
            
            input("\n按回车键继续...")


def main():
    """主函数"""
    try:
        analyzer = SSQAnalyzer()
        analyzer.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错：{str(e)}")


if __name__ == "__main__":
    main()
