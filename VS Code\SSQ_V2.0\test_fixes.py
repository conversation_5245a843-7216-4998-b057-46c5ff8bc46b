#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
验证遗漏值计算和初选号码逻辑是否正确
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_reader import DataReader
from modules.statistical_analysis import StatisticalAnalysis
from modules.core_algorithms import CoreAlgorithms


def test_missing_value_calculation():
    """测试遗漏值计算修复"""
    print("=" * 50)
    print("测试遗漏值计算修复")
    print("=" * 50)
    
    try:
        # 读取数据
        data_reader = DataReader()
        ssq_history = data_reader.read_ssq_history()
        parameters = data_reader.read_parameters()
        
        # 创建统计分析实例
        analysis = StatisticalAnalysis(ssq_history, parameters)
        
        # 测试单期遗漏值计算
        test_period = "25002"  # 使用一个测试期号
        
        # 获取测试期的实际数据
        period_int = int(test_period)
        test_row = ssq_history[ssq_history['期号'] == period_int]
        
        if test_row.empty:
            print(f"未找到期号 {test_period} 的数据")
            return
        
        test_row = test_row.iloc[0]
        red_balls = [test_row['红球1'], test_row['红球2'], test_row['红球3'],
                    test_row['红球4'], test_row['红球5'], test_row['红球6']]
        blue_ball = test_row['蓝球']
        
        print(f"测试期号：{test_period}")
        print(f"红蓝球号码：{red_balls} + {blue_ball}")
        
        # 构建历史数据库
        hist_db = analysis._build_history_database(test_period)
        print(f"历史数据库期数：{len(hist_db)}")
        
        # 计算遗漏值
        missing_values = analysis._calculate_missing_values_correct(red_balls, blue_ball, hist_db)
        
        print(f"计算的遗漏值：{missing_values}")
        
        # 验证遗漏值计算逻辑
        print("\n验证遗漏值计算逻辑：")
        for i, ball in enumerate(red_balls):
            missing = analysis._calculate_single_ball_missing(ball, False, hist_db)
            print(f"红球 {ball} 的遗漏值：{missing}")
        
        blue_missing = analysis._calculate_single_ball_missing(blue_ball, True, hist_db)
        print(f"蓝球 {blue_ball} 的遗漏值：{blue_missing}")
        
        print("遗漏值计算测试完成！")
        
    except Exception as e:
        print(f"遗漏值计算测试失败：{str(e)}")


def test_initial_selection_logic():
    """测试初选号码逻辑修复"""
    print("\n" + "=" * 50)
    print("测试初选号码逻辑修复")
    print("=" * 50)
    
    try:
        # 读取数据
        data_reader = DataReader()
        ssq_history = data_reader.read_ssq_history()
        parameters = data_reader.read_parameters()
        
        # 创建核心算法实例
        core_algorithms = CoreAlgorithms()
        
        # 使用最近100期数据进行测试
        test_data = ssq_history.tail(100)
        
        # 计算历史概率
        red_hist_prob, blue_hist_prob = core_algorithms.calculate_historical_probability(test_data)
        
        # 排序概率
        red_hist_sorted = core_algorithms.sort_by_probability(red_hist_prob)
        blue_hist_sorted = core_algorithms.sort_by_probability(blue_hist_prob)
        
        print("红球历史概率排序前10名：")
        print(red_hist_sorted.head(10))
        
        print("\n蓝球历史概率排序前10名：")
        print(blue_hist_sorted.head(10))
        
        # 测试按排序序号选择号码
        HXR = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 22]  # 示例排序序号
        HXB = [1, 2, 3, 4, 5, 6]  # 示例排序序号
        
        print(f"\n测试排序序号 HXR: {HXR}")
        print(f"测试排序序号 HXB: {HXB}")
        
        # 按排序序号选择红球
        selected_red = set()
        for rank in HXR:
            if rank <= len(red_hist_sorted):
                ball = red_hist_sorted.iloc[rank - 1]['号码']
                selected_red.add(ball)
        
        # 按排序序号选择蓝球
        selected_blue = set()
        for rank in HXB:
            if rank <= len(blue_hist_sorted):
                ball = blue_hist_sorted.iloc[rank - 1]['号码']
                selected_blue.add(ball)
        
        print(f"\n选出的红球号码：{sorted(list(selected_red))}")
        print(f"选出的蓝球号码：{sorted(list(selected_blue))}")
        print(f"红球数量：{len(selected_red)}，蓝球数量：{len(selected_blue)}")
        
        # 测试马尔科夫链算法
        print("\n测试马尔科夫链算法...")
        latest_row = test_data.iloc[-1]
        latest_red = [latest_row['红球1'], latest_row['红球2'], latest_row['红球3'],
                     latest_row['红球4'], latest_row['红球5'], latest_row['红球6']]
        latest_blue = latest_row['蓝球']
        
        print(f"最新一期红蓝球：{latest_red} + {latest_blue}")
        
        # 计算跟随概率
        red_follow_prob, blue_follow_prob = core_algorithms.calculate_following_probability(test_data)
        
        # 马尔科夫链算法
        red_migration, blue_migration = core_algorithms.markov_chain_algorithm(
            latest_red, latest_blue, red_hist_prob, blue_hist_prob,
            red_follow_prob, blue_follow_prob
        )
        
        # 排序迁移概率
        red_migration_sorted = core_algorithms.sort_by_probability(red_migration)
        blue_migration_sorted = core_algorithms.sort_by_probability(blue_migration)
        
        print("红球迁移概率排序前10名：")
        print(red_migration_sorted.head(10))
        
        print("\n蓝球迁移概率排序前10名：")
        print(blue_migration_sorted.head(10))
        
        # 按排序序号选择迁移概率号码
        FXR = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 22]  # 示例排序序号
        FXB = [1, 2, 3, 4, 5, 6]  # 示例排序序号
        
        selected_red_migration = set()
        for rank in FXR:
            if rank <= len(red_migration_sorted):
                ball = red_migration_sorted.iloc[rank - 1]['号码']
                selected_red_migration.add(ball)
        
        selected_blue_migration = set()
        for rank in FXB:
            if rank <= len(blue_migration_sorted):
                ball = blue_migration_sorted.iloc[rank - 1]['号码']
                selected_blue_migration.add(ball)
        
        print(f"\n迁移概率选出的红球号码：{sorted(list(selected_red_migration))}")
        print(f"迁移概率选出的蓝球号码：{sorted(list(selected_blue_migration))}")
        print(f"红球数量：{len(selected_red_migration)}，蓝球数量：{len(selected_blue_migration)}")
        
        print("\n初选号码逻辑测试完成！")
        
    except Exception as e:
        print(f"初选号码逻辑测试失败：{str(e)}")


def main():
    """主测试函数"""
    print("SSQ分析系统修复功能测试")
    print("=" * 50)
    
    # 测试遗漏值计算修复
    test_missing_value_calculation()
    
    # 测试初选号码逻辑修复
    test_initial_selection_logic()
    
    print("\n" + "=" * 50)
    print("所有修复功能测试完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
