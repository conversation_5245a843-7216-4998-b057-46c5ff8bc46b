#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试脚本
用于验证核心算法和数据读取功能
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_reader import DataReader
from modules.core_algorithms import CoreAlgorithms
from modules.utils import Utils


def test_data_reading():
    """测试数据读取功能"""
    print("=" * 50)
    print("测试数据读取功能")
    print("=" * 50)
    
    try:
        data_reader = DataReader()
        
        # 测试读取SSQ历史数据
        print("正在读取SSQ历史数据...")
        ssq_history = data_reader.read_ssq_history()
        print(f"成功读取 {len(ssq_history)} 期数据")
        
        # 显示前5期数据
        print("\n前5期数据：")
        print(ssq_history.head())
        
        # 测试读取参数配置
        print("\n正在读取参数配置...")
        parameters = data_reader.read_parameters()
        print("参数配置：")
        for key, value in parameters.items():
            print(f"  {key}: {value}")
        
        return ssq_history, parameters
        
    except Exception as e:
        print(f"数据读取测试失败：{str(e)}")
        return None, None


def test_core_algorithms(ssq_history, parameters):
    """测试核心算法功能"""
    print("\n" + "=" * 50)
    print("测试核心算法功能")
    print("=" * 50)
    
    if ssq_history is None:
        print("无法测试核心算法：数据读取失败")
        return
    
    try:
        core_algorithms = CoreAlgorithms()
        
        # 使用最近100期数据进行测试
        test_data = ssq_history.tail(100)
        print(f"使用最近 {len(test_data)} 期数据进行测试")
        
        # 测试历史概率计算
        print("\n正在计算历史出现概率...")
        red_hist_prob, blue_hist_prob = core_algorithms.calculate_historical_probability(test_data)
        print(f"红球概率表：{len(red_hist_prob)} 行")
        print(f"蓝球概率表：{len(blue_hist_prob)} 行")
        
        # 显示前5个红球概率
        print("\n前5个红球概率：")
        print(red_hist_prob.head())
        
        # 测试跟随概率计算
        print("\n正在计算历史跟随性概率...")
        red_follow_prob, blue_follow_prob = core_algorithms.calculate_following_probability(test_data)
        print(f"红球跟随概率矩阵：{red_follow_prob.shape}")
        print(f"蓝球跟随概率矩阵：{blue_follow_prob.shape}")
        
        # 测试马尔科夫链算法
        print("\n正在测试马尔科夫链算法...")
        latest_row = test_data.iloc[-1]
        latest_red = [latest_row['红球1'], latest_row['红球2'], latest_row['红球3'],
                     latest_row['红球4'], latest_row['红球5'], latest_row['红球6']]
        latest_blue = latest_row['蓝球']
        
        red_migration, blue_migration = core_algorithms.markov_chain_algorithm(
            latest_red, latest_blue, red_hist_prob, blue_hist_prob,
            red_follow_prob, blue_follow_prob
        )
        
        print(f"红球迁移概率表：{len(red_migration)} 行")
        print(f"蓝球迁移概率表：{len(blue_migration)} 行")
        
        # 测试概率排序
        print("\n正在测试概率排序...")
        red_sorted = core_algorithms.sort_by_probability(red_hist_prob)
        blue_sorted = core_algorithms.sort_by_probability(blue_hist_prob)
        
        print("红球概率排序前5名：")
        print(red_sorted.head())
        
        print("\n蓝球概率排序前5名：")
        print(blue_sorted.head())
        
        print("\n核心算法测试完成！")
        
    except Exception as e:
        print(f"核心算法测试失败：{str(e)}")


def test_utils_functions():
    """测试工具函数"""
    print("\n" + "=" * 50)
    print("测试工具函数")
    print("=" * 50)
    
    try:
        utils = Utils()
        
        # 测试期号格式验证
        print("测试期号格式验证：")
        test_periods = ["23001", "14100", "7001", "abc", "123"]
        for period in test_periods:
            valid = utils.validate_period_format(period)
            print(f"  {period}: {'有效' if valid else '无效'}")
        
        # 测试号码特性判断
        print("\n测试号码特性判断：")
        test_numbers = [1, 8, 9, 16, 17, 25, 33]
        for num in test_numbers:
            big = utils.is_big_ball(num, False)
            odd = utils.is_odd_ball(num)
            prime = utils.is_prime_ball(num)
            print(f"  {num}: 大球={big}, 奇球={odd}, 质球={prime}")
        
        # 测试AC值计算
        print("\n测试AC值计算：")
        test_red_balls = [6, 10, 17, 19, 25, 31]
        ac_value = utils.calculate_ac_value(test_red_balls)
        print(f"  红球 {test_red_balls} 的AC值: {ac_value}")
        
        # 测试和值计算
        sum_value = utils.calculate_sum(test_red_balls)
        print(f"  红球 {test_red_balls} 的和值: {sum_value}")
        
        # 测试号码显示格式化
        formatted = utils.format_balls_display(test_red_balls, 11)
        print(f"  格式化显示: {formatted}")
        
        print("\n工具函数测试完成！")
        
    except Exception as e:
        print(f"工具函数测试失败：{str(e)}")


def main():
    """主测试函数"""
    print("SSQ分析系统基础功能测试")
    print("=" * 50)
    
    # 测试数据读取
    ssq_history, parameters = test_data_reading()
    
    # 测试核心算法
    test_core_algorithms(ssq_history, parameters)
    
    # 测试工具函数
    test_utils_functions()
    
    print("\n" + "=" * 50)
    print("所有基础功能测试完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
